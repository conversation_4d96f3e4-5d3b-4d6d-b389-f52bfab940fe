<script setup>
// Footer组件不需要额外的逻辑
</script>

<template>
  <footer id="contact" class="relative z-40 bg-transparent border-t border-white/20">
    <div class="container mx-auto px-4 py-8">
      <div class="flex flex-col md:flex-row justify-around items-start gap-8 max-w-6xl mx-auto">
        <!-- 公司信息 -->
        <div>
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center">
              <span class="text-white font-bold">帆</span>
            </div>
            <span class="text-xl font-bold text-white">上海帆前科技</span>
          </div>
          <p class="text-gray-300 mb-4">
            {{ $t('footer.description') }}
          </p>
          <div class="flex space-x-3">
            <!-- <UButton color="gray" variant="ghost" icon="i-heroicons-globe-alt" />
            <UButton color="gray" variant="ghost" icon="i-heroicons-facebook" />
            <UButton color="gray" variant="ghost" icon="i-heroicons-twitter" />
            <UButton color="gray" variant="ghost" icon="i-heroicons-github" /> -->
          </div>
        </div>

        <!-- 服务 -->
        <!-- <div>
          <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{{ $t('footer.services') }}</h3>
          <ul class="space-y-2">
            <li v-for="service in services" :key="service">
              <NuxtLink to="/services" class="text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400">
                {{ service }}
              </NuxtLink>
            </li>
          </ul>
        </div> -->

        <!-- 联系方式 -->
        <div>
          <h3 class="text-lg font-semibold mb-4 text-white">
            {{ $t('footer.contact') }}
          </h3>
          <ul class="space-y-3">
            <li class="flex items-start">
              <UIcon name="i-heroicons-map-pin" class="w-5 h-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <span class="text-gray-300">上海市浦东新区张江高科技园区</span>
            </li>
            <li class="flex items-start">
              <UIcon name="i-heroicons-phone" class="w-5 h-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <span class="text-gray-300">+86 21 8888 8888</span>
            </li>
            <li class="flex items-start">
              <UIcon name="i-heroicons-envelope" class="w-5 h-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <span class="text-gray-300"><EMAIL></span>
            </li>
            <li class="flex items-start">
              <UIcon name="i-heroicons-clock" class="w-5 h-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
              <span class="text-gray-300">周一至周五: 9:00 - 18:00</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="mt-8 pt-8 border-t border-white/20 text-center text-gray-400 text-sm">
        © {{ new Date().getFullYear() }} 上海帆前科技. {{ $t('footer.rights') }}
      </div>
    </div>
  </footer>
</template>
