<script lang="ts" setup>
import type { DropdownMenuItem } from '@nuxt/ui'
// const { locale, setLocale } = useI18n()

// const availableLocales = computed(() => {
//   return locales.value.filter(l => l.code !== locale.value)
// })

const items = ref<DropdownMenuItem[]>([
  {
    label: 'Profile',
    icon: 'i-lucide-user'
  },
  {
    label: 'Billing',
    icon: 'i-lucide-credit-card'
  },
  {
    label: 'Settings',
    icon: 'i-lucide-cog'
  }
])
</script>

<template>
  <UDropdownMenu :items="items" :content="{
    align: 'start',
    side: 'bottom',
    sideOffset: 8
  }" :ui="{
      content: 'w-48'
    }">
    <UButton label="Open" icon="i-lucide-menu" color="neutral" variant="outline" />
  </UDropdownMenu>
  <!-- <UDropdown :items="[[{ label: locale.name }], availableLocales.map(l => ({
    label: l.name,
    click: () => setLocale(l.code)
  }))]">
    <UButton variant="ghost" color="gray">
      <UIcon name="material-symbols:language" class="size-5 mr-1" />
      {{ locale.name }}
    </UButton>
  </UDropdown> -->
</template>
