<script setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const localePath = useLocalePath()
const { setLocale } = useI18n()

const { locale, t } = useI18n()
const isMobileMenuOpen = ref(false)

// 平滑滚动函数
const smoothScrollTo = (targetId) => {
  const element = document.getElementById(targetId.replace('#', ''))
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 处理导航点击
const handleNavClick = (to) => {
  if (to.startsWith('#')) {
    smoothScrollTo(to)
    isMobileMenuOpen.value = false // 关闭移动端菜单
  } else if (to === localePath('/')) {
    // 如果是首页链接，滚动到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
    isMobileMenuOpen.value = false // 关闭移动端菜单
  } else {
    // 如果是其他普通链接，使用 navigateTo
    navigateTo(to)
  }
}

// 导航链接
const navLinks = computed(() => [
  { label: t('home'), to: localePath('/') },
  { label: t('services'), to: '#services' },
  { label: t('about'), to: '#about' },
  { label: t('portfolio'), to: '#portfolio' },
  { label: t('contact'), to: '#contact' },
])

// 语言选项 - 使用与 nuxt.config.ts 中一致的语言代码
const languages = [
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
  { code: 'zh-CN', name: '中文', flag: '🇨🇳' },
]

// 当前语言
const currentLanguage = computed(() => {
  return languages.find(lang => lang.code === locale.value) || languages[0]
})

// // 语言切换器选项 - 使用 UDropdownMenu 的 items 结构
// const languageItems = computed(() => [
//   [
//     // 标题项
//     {
//       label: t('select_language'),
//       type: 'label',
//       disabled: true,
//     },
//   ],
//   // 语言选项
//   ...languages.map(lang => ({
//     label: lang.name,
//     flag: lang.flag,
//     click: () => {
//       locale.value = lang.code
//     },
//   })),
// ])
</script>

<template>
  <header class="sticky top-0 z-50 backdrop-blur-md bg-white/10 border-b border-white/20 shadow-lg">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
      <!-- Logo 区域 -->
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 rounded-full bg-primary-500 flex items-center justify-center">
          <span class="text-white font-bold text-xl">帆</span>
        </div>
        <span class="text-xl font-bold text-white">上海帆前科技</span>
      </div>

      <!-- 桌面导航菜单 -->
      <nav class="hidden md:flex space-x-8">
        <a v-for="link in navLinks" :key="link.label" :href="link.to" @click.prevent="handleNavClick(link.to)"
          class="text-white/90 hover:text-white transition-colors cursor-pointer">
          {{ link.label }}
        </a>
      </nav>
      <!-- 右侧功能区 -->
      <div class="flex items-center space-x-4">
        <!-- 语言切换器 - 使用 UDropdownMenu -->
        <UDropdownMenu :items="[languages.map(lang => ({
          label: lang.name,
          icon: lang.flag,
          onSelect: () => setLocale(lang.code)
        }))]" :ui="{ content: 'w-48' }">
          <UButton color="white" variant="ghost" :label="currentLanguage.name"
            trailing-icon="i-heroicons-chevron-down-20-solid"
            class="text-white/90 hover:text-white hover:bg-white/10" />
        </UDropdownMenu>

        <!-- 颜色模式切换 -->
        <!-- <UButton color="white" variant="ghost"
          :icon="colorMode.value === 'dark' ? 'i-heroicons-sun-20-solid' : 'i-heroicons-moon-20-solid'"
          class="text-white/90 hover:text-white hover:bg-white/10" @click="toggleColorMode" /> -->

        <!-- 移动端菜单 -->
        <USlideover v-model:open="isMobileMenuOpen" side="left" :ui="{
          content: 'fixed bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white divide-y divide-white/20 sm:ring ring-white/20 sm:shadow-lg flex flex-col focus:outline-none w-2/3 max-w-sm'
        }">
          <!-- 移动端菜单按钮 (默认插槽) -->
          <UButton color="white" variant="ghost" icon="i-heroicons-bars-3-20-solid"
            class="md:hidden text-white/90 hover:text-white hover:bg-white/10" />

          <!-- 移动端菜单内容 -->
          <template #content>
            <div class="flex flex-col h-full">
              <div class="flex items-center justify-between p-4 border-b border-white/20">
                <h3 class="text-lg font-semibold text-white">
                  {{ $t('mobile_menu_title') }}
                </h3>
                <UButton color="white" variant="ghost" icon="i-heroicons-x-mark-20-solid"
                  class="text-white/90 hover:text-white hover:bg-white/10" @click="isMobileMenuOpen = false" />
              </div>

              <div class="flex-1 overflow-y-auto p-4 space-y-2">
                <a v-for="link in navLinks" :key="link.label" :href="link.to" @click.prevent="handleNavClick(link.to)"
                  class="block py-3 px-4 rounded-lg text-white/90 hover:text-white hover:bg-white/10 transition-colors cursor-pointer">
                  {{ link.label }}
                </a>
              </div>
            </div>
          </template>
        </USlideover>
      </div>
    </div>
  </header>
</template>

<style scoped>
/* 导航链接活动状态指示器 */
.router-link-active {
  position: relative;
}

.router-link-active:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary-500);
  border-radius: 1px;
}

.dark .router-link-active:after {
  background-color: var(--color-primary-400);
}
</style>
