// @eslint-disable
import antfu from '@antfu/eslint-config'
import i18nPlugin from '@intlify/eslint-plugin-vue-i18n'

export default antfu(
  {
    typescript: true,
    vue: true,
    stylistic: true,
  },
  {
    plugins: {
      '@intlify/vue-i18n': i18nPlugin,
    },
    rules: {
      // 自定义规则覆盖
      'vue/no-v-html': 'off',
      'vue/multi-word-component-names': 'off',
      // 禁用 eol-last 规则
      'eol-last': 'off',
    },
  },
  {
    // 针对 i18n 的特定规则
    files: ['**/*.vue'],
    rules: {
      '@intlify/vue-i18n/no-dynamic-keys': 'error',
      '@intlify/vue-i18n/no-unused-keys': 'error',
      '@intlify/vue-i18n/no-raw-text': 'warn',
    },
  },
)
