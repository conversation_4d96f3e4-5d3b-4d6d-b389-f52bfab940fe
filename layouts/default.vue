<script setup>
const { t } = useI18n()
const route = useRoute()
const appConfig = useAppConfig()

useHead({
  titleTemplate: (title) => {
    return title ? `${title} - ${appConfig.name}` : appConfig.name
  }
})
</script>

<template>
  <div class="flex flex-col min-h-screen bg-white dark:bg-gray-900">
    <!-- 导航栏 -->
    <Nav />

    <!-- 主内容区域 -->
    <main class="flex-grow">
      <slot />
    </main>

    <!-- 页脚 -->
    <Footer />
  </div>
</template>

<style scoped>
main {
  min-height: calc(100vh - 120px);
  /* 确保内容区域足够高度 */
}
</style>