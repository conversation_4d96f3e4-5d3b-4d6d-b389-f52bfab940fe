// import path from 'path'
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  css: ["~/assets/css/main.css"],
  devtools: { enabled: true },
  modules: ["@nuxt/ui", "@nuxtjs/i18n", "@pinia/nuxt"],

  // 添加运行时配置
  runtimeConfig: {
    public: {
      i18n: {
        defaultLocale: "zh-CN",
        baseUrl: "https://your-domain.com",
      },
    },
  },
  // i18n 配置
  i18n: {
    // 默认语言不加前缀，其他语言加前缀
    strategy: "prefix_except_default",
    // 配置标志来设置语言环境消息的行为编译。
    compilation: {
      // 严格检查区域设置消息不包含 HTML 标记。如果包含 HTML 标签，则会引发错误。
      strictMessage: false,
      // 确定是否转义 HTML 标记（如果它们包含在区域设置消息中）。
      escapeHtml: true,
    },
    // 默认语言
    defaultLocale: "zh-CN",
    // 浏览器语言自动检测配置
    detectBrowserLanguage: {
      useCookie: true, // 使用 cookie 保存用户选择的语言
      cookieKey: "i18n_redirected", // cookie 键名
      redirectOn: "root", // 仅在根路径进行重定向
      alwaysRedirect: false, // 不总是重定向
      fallbackLocale: "zh-CN", // 当检测到的语言不支持时的回退语言
      cookieCrossOrigin: false,
      cookieSecure: false,
    },
    restructureDir: "",
    vueI18n: "./locales/vue-i18n.options.ts",
    // vueI18n: path.resolve(__dirname, 'locales/vue-i18n.options.ts'),
    // 存放语言文件的地方
    langDir: "./locales",
    // langDir: path.resolve(__dirname, 'locales'),
    // 懒加载，被使用到的时候才去请求该语言文件
    lazy: false,
    locales: [
      {
        code: "zh-CN",
        iso: "zh-CN",
        name: "中文",
        file: "zh.json",
      },
      {
        code: "en-US",
        iso: "en-US",
        name: "English",
        file: "en.json",
      },
    ],
  },
  // UI 模块配置
  ui: {
    global: true,
    fonts: false, // 禁用自动字体加载
  },
  pinia: {
    autoImports: [
      "defineStore", // import { defineStore } from 'pinia'
    ],
  },
  // 确保组件自动导入
  components: {
    global: true,
    dirs: ["~/components"],
  },
  // Tailwind 集成配置
  postcss: {
    plugins: {
      "@tailwindcss/postcss": {
        // 使用新插件名称
        config: "./tailwind.config.js", // 确保路径正确
      },
      autoprefixer: {},
    },
  },

  // 更新 TypeScript 配置
  typescript: {
    strict: true,
    // 确保 typeCheck 设置为 true 时已安装 vue-tsc
    typeCheck: true,
    // 显式指定 tsConfig 路径
    tsConfig: {
      include: ["**/*.ts", "**/*.vue", ".nuxt/**/*.d.ts"],
      exclude: ["node_modules", "dist"],
    },
  },
  // 确保 vite 配置正确
  vite: {
    define: {
      __MY_APP_VERSION__: JSON.stringify("1.0.0"),
      __API_BASE__: JSON.stringify(process.env.VITE_API_BASE_URL),
    },
    build: {
      sourcemap: process.env.NODE_ENV === "development",
    },

    // 添加插件配置（可选）
    plugins: [
      // 如果你使用 vite-plugin-checker，确保正确配置
      // 否则可以移除相关插件
    ],
  },
});
