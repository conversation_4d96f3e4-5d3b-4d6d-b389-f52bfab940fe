{"name": "nuxt3_i18n_official_template", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint:style": "stylelint \"**/*.{vue,css,scss,postcss}\"", "lint:ts": "tsc -p tsconfig.json --noEmit", "lint": "pnpm run lint:style && pnpm run lint:ts"}, "dependencies": {"@nuxt/ui": "^3.1.3", "@nuxtjs/i18n": "^9.5.4", "@pinia/nuxt": "^0.11.0", "nuxt": "^3.17.4", "pinia": "^3.0.2", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.13.3", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/lucide": "^1.2.57", "@intlify/eslint-plugin-vue-i18n": "^4.0.1", "@stylistic/eslint-plugin": "^4.4.0", "@stylistic/stylelint-config": "^2.0.0", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.24", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.0", "tailwindcss": "^4.1.7", "vue-tsc": "^2.2.10"}}