<script setup>
const { t } = useI18n()
const localePath = useLocalePath()

// 动画效果
const isVisible = ref(false)
const heroRef = ref(null)

// 粒子系统
const particles = ref([])
const mouseParticles = ref([])
const particleCount = 50
const mousePosition = ref({ x: 0, y: 0 })
const isMouseMoving = ref(false)
let mouseTimeout = null

// 初始化粒子
const initParticles = () => {
  particles.value = []
  for (let i = 0; i < particleCount; i++) {
    particles.value.push({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 3 + 1,
      speedX: (Math.random() - 0.5) * 0.5,
      speedY: (Math.random() - 0.5) * 0.5,
      opacity: Math.random() * 0.5 + 0.2
    })
  }
}

// 创建鼠标粒子
const createMouseParticle = (x, y) => {
  console.log('Creating mouse particle at:', x, y) // 调试信息
  const particle = {
    id: Date.now() + Math.random(),
    x,
    y,
    size: Math.random() * 8 + 4, // 增大粒子尺寸便于观察
    speedX: (Math.random() - 0.5) * 3,
    speedY: (Math.random() - 0.5) * 3,
    opacity: 1.0, // 增加不透明度
    life: 1.0,
    decay: 0.01 // 减慢衰减速度
  }
  mouseParticles.value.push(particle)
  console.log('Mouse particles count:', mouseParticles.value.length) // 调试信息

  // 限制鼠标粒子数量，避免性能问题
  if (mouseParticles.value.length > 20) {
    mouseParticles.value.shift()
  }
}

// 鼠标移动处理
const handleMouseMove = (event) => {
  console.log('Mouse move detected:', event.clientX, event.clientY) // 调试信息
  mousePosition.value.x = event.clientX
  mousePosition.value.y = event.clientY
  isMouseMoving.value = true

  // 简化：每次鼠标移动都创建粒子（临时测试）
  createMouseParticle(event.clientX, event.clientY)
}

// 增强的粒子动画（包含鼠标交互）
const animateParticles = () => {
  // 更新背景粒子
  particles.value.forEach(particle => {
    particle.x += particle.speedX
    particle.y += particle.speedY

    // 边界检测
    if (particle.x < 0 || particle.x > window.innerWidth) particle.speedX *= -1
    if (particle.y < 0 || particle.y > window.innerHeight) particle.speedY *= -1
  })

  // 更新鼠标粒子
  mouseParticles.value = mouseParticles.value.filter(particle => {
    particle.x += particle.speedX
    particle.y += particle.speedY
    particle.life -= particle.decay
    particle.opacity = particle.life * 0.8

    return particle.life > 0
  })

  requestAnimationFrame(animateParticles)
}

// 平滑滚动函数
function smoothScrollTo(targetId) {
  const element = document.getElementById(targetId.replace('#', ''))
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

onMounted(() => {
  isVisible.value = true

  // 初始化粒子系统
  if (typeof window !== 'undefined') {
    initParticles()
    animateParticles()

    // 添加鼠标移动监听
    window.addEventListener('mousemove', handleMouseMove)
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('mousemove', handleMouseMove)
  }
})

// 获取服务背景颜色类
function getServiceBgColor(color) {
  const colorMap = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    cyan: 'bg-cyan-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500'
  }
  return colorMap[color] || 'bg-blue-500'
}

// 获取项目类型颜色类
function getProjectTypeColor(type) {
  const typeColorMap = {
    企业应用: 'bg-blue-500 text-white',
    移动应用: 'bg-green-500 text-white',
    AI解决方案: 'bg-purple-500 text-white',
    区块链: 'bg-orange-500 text-white',
    IoT解决方案: 'bg-cyan-500 text-white',
    网站开发: 'bg-indigo-500 text-white'
  }
  return typeColorMap[type] || 'bg-gray-500 text-white'
}

// 服务数据
const services = computed(() => [
  {
    icon: 'i-lucide-code',
    title: t('services_section.web_dev.title'),
    description: t('services_section.web_dev.description'),
    color: 'blue'
  },
  {
    icon: 'i-lucide-smartphone',
    title: t('services_section.mobile_dev.title'),
    description: t('services_section.mobile_dev.description'),
    color: 'green'
  },
  {
    icon: 'i-lucide-brain',
    title: t('services_section.ai_ml.title'),
    description: t('services_section.ai_ml.description'),
    color: 'purple'
  },
  {
    icon: 'i-lucide-cloud',
    title: t('services_section.cloud.title'),
    description: t('services_section.cloud.description'),
    color: 'cyan'
  },
  {
    icon: 'i-lucide-blocks',
    title: t('services_section.blockchain.title'),
    description: t('services_section.blockchain.description'),
    color: 'orange'
  },
  {
    icon: 'i-lucide-settings',
    title: t('services_section.devops.title'),
    description: t('services_section.devops.description'),
    color: 'red'
  }
])

// 特性数据
const features = computed(() => [
  {
    icon: 'i-lucide-zap',
    title: t('features.item1.title'),
    description: t('features.item1.description')
  },
  {
    icon: 'i-lucide-shield',
    title: t('features.item2.title'),
    description: t('features.item2.description')
  },
  {
    icon: 'i-lucide-headphones',
    title: t('features.item3.title'),
    description: t('features.item3.description')
  },
  {
    icon: 'i-lucide-rocket',
    title: t('features.item4.title'),
    description: t('features.item4.description')
  },
  {
    icon: 'i-lucide-lock',
    title: t('features.item5.title'),
    description: t('features.item5.description')
  },
  {
    icon: 'i-lucide-globe',
    title: t('features.item6.title'),
    description: t('features.item6.description')
  }
])

// 作品集项目数据
const portfolioProjects = [
  {
    title: '智能企业管理系统',
    description: '为大型制造企业开发的一体化管理平台，整合ERP、CRM和供应链管理。',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop',
    type: '企业应用',
    technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Docker']
  },
  {
    title: '电商移动应用',
    description: '全功能电子商务移动应用，支持在线支付、实时库存和个性化推荐。',
    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop',
    type: '移动应用',
    technologies: ['React Native', 'Firebase', 'Redux', 'Stripe']
  },
  {
    title: 'AI驱动的数据分析平台',
    description: '利用机器学习算法分析大数据，提供可视化报告和预测分析。',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop',
    type: 'AI解决方案',
    technologies: ['Python', 'TensorFlow', 'D3.js', 'AWS']
  },
  {
    title: '区块链资产交易平台',
    description: '安全、高效的数字资产交易平台，基于区块链技术构建。',
    image: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=500&h=300&fit=crop',
    type: '区块链',
    technologies: ['Solidity', 'Web3.js', 'React', 'Node.js']
  },
  {
    title: '智慧城市监控系统',
    description: '实时监控和管理城市基础设施的综合平台，包括交通、能源和安全。',
    image: 'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=500&h=300&fit=crop',
    type: 'IoT解决方案',
    technologies: ['Angular', 'Python', 'MongoDB', 'MQTT']
  },
  {
    title: '健康管理应用',
    description: '个人健康追踪和管理应用，集成可穿戴设备数据和健康建议。',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500&h=300&fit=crop',
    type: '移动应用',
    technologies: ['Flutter', 'Firebase', 'HealthKit', 'Google Fit']
  }
]

// 统计数据
const stats = computed(() => [
  { label: t('hero.stats.projects'), value: '500+' },
  { label: t('hero.stats.clients'), value: '200+' },
  { label: t('hero.stats.experience'), value: '8+' },
  { label: t('hero.stats.technologies'), value: '50+' }
])
</script>

<template>
  <div>
    <!-- 固定背景层 -->
    <div
      class="fixed inset-0 w-full h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 z-0">
      <!-- 背景动画效果 -->

      <!-- 动态网格背景 -->
      <div class="absolute inset-0 opacity-30">
        <div class="absolute inset-0 bg-grid-pattern"></div>
      </div>

      <!-- 粒子系统 -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- 背景粒子 -->
        <div v-for="particle in particles" :key="particle.id" class="absolute rounded-full bg-white" :style="{
          left: particle.x + 'px',
          top: particle.y + 'px',
          width: particle.size + 'px',
          height: particle.size + 'px',
          opacity: particle.opacity
        }"></div>

        <!-- 鼠标粒子 -->
        <div v-for="particle in mouseParticles" :key="particle.id" class="absolute rounded-full bg-primary-500 z-50"
          :style="{
            left: (particle.x - particle.size / 2) + 'px',
            top: (particle.y - particle.size / 2) + 'px',
            width: particle.size + 'px',
            height: particle.size + 'px',
            opacity: particle.opacity,
            pointerEvents: 'none'
          }">
        </div>
      </div>

      <!-- 静态光效 -->
      <div class="absolute inset-0 opacity-40">
        <div
          class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl animate-float">
        </div>
        <div
          class="absolute top-1/3 right-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl animate-float-delayed">
        </div>
        <div
          class="absolute bottom-1/4 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-3xl animate-float-slow">
        </div>
      </div>

      <!-- 扫描线效果 -->
      <div class="scan-line"></div>
    </div>

    <!-- Hero Section - 内容区域 -->
    <section ref="heroRef" class="relative min-h-screen text-white flex items-center z-10">
      <UContainer class="relative z-40 py-20 lg:py-32">
        <div class="text-center max-w-5xl mx-auto">
          <div class="transition-all duration-1000"
            :class="{ 'translate-y-0 opacity-100': isVisible, 'translate-y-10 opacity-0': !isVisible }">

            <!-- 主标题 -->
            <div class="relative mb-8">
              <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-8xl font-bold mb-6 leading-tight transform-3d">
                <span class="block typewriter">{{ t('hero.title') }}</span>
                <span
                  class="block gradient-text text-4xl sm:text-5xl md:text-6xl lg:text-9xl font-black mt-4 hover-glow transition-all duration-500">
                  {{ t('hero.highlight') }}
                </span>
              </h1>

              <!-- 装饰性元素 -->
              <div class="absolute -top-4 -left-4 w-8 h-8 border-l-2 border-t-2 border-cyan-400 opacity-60"></div>
              <div class="absolute -bottom-4 -right-4 w-8 h-8 border-r-2 border-b-2 border-purple-400 opacity-60"></div>
            </div>

            <!-- 副标题 -->
            <div class="relative">
              <p
                class="text-xl lg:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed backdrop-blur-sm bg-white/5 rounded-2xl p-6 border border-white/10">
                {{ t('hero.subtitle') }}
              </p>
            </div>

            <!-- 按钮组 -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center mb-20">
              <UButton size="xl" color="primary" variant="solid"
                class="px-10 py-5 text-lg font-semibold btn-hover pulse-glow transition-all duration-500 rounded-2xl"
                href="#contact" @click.prevent="smoothScrollTo('contact')">
                <UIcon name="i-lucide-rocket" class="mr-3 text-xl" />
                {{ t('hero.cta_primary') }}
              </UButton>

              <UButton size="xl" color="white" variant="outline"
                class="px-10 py-5 text-lg font-semibold btn-hover transition-all duration-500 rounded-2xl border-2 hover:bg-white/10"
                href="#portfolio" @click.prevent="smoothScrollTo('portfolio')">
                <UIcon name="i-lucide-eye" class="mr-3 text-xl" />
                {{ t('hero.cta_secondary') }}
              </UButton>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20">
            <div v-for="(stat, index) in stats" :key="index"
              class="text-center card-hover transition-all duration-500 backdrop-blur-sm bg-white/5 rounded-2xl p-6 border border-white/10"
              :style="{ animationDelay: `${index * 200}ms` }">
              <div
                class="text-4xl lg:text-5xl font-black text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text mb-3">
                {{ stat.value }}
              </div>
              <div class="text-gray-300 text-sm lg:text-base font-medium">{{ stat.label }}</div>

              <!-- 装饰性粒子线 -->
              <div class="particle-line" :style="{ animationDelay: `${index * 500}ms` }"></div>
            </div>
          </div>

          <!-- 滚动提示 -->
          <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="flex flex-col items-center space-y-2">
              <div class="w-6 h-10 border-2 border-white/30 rounded-full flex items-center justify-center">
                <div class="w-1 h-3 bg-white/60 rounded-full animate-pulse" />
              </div>
              <p class="text-white/60 text-sm">
                {{ t('hero.scroll_down') }}
              </p>
            </div>
          </div>
        </div>
      </UContainer>
    </section>

    <!-- Services Section -->
    <section id="services" class="relative z-40 py-20 text-white">
      <UContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
            {{ t('services_section.title') }}
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            {{ t('services_section.subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <UCard v-for="(service, index) in services" :key="index"
            class="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-0 backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20"
            :ui="{ body: { padding: 'p-8' } }">
            <div class="text-center">
              <div class="mb-6">
                <div
                  class="w-16 h-16 mx-auto rounded-full flex items-center justify-center text-white text-2xl transform group-hover:scale-110 transition-all duration-300"
                  :class="getServiceBgColor(service.color)">
                  <UIcon :name="service.icon" />
                </div>
              </div>

              <h3 class="text-xl font-bold text-white mb-4">
                {{ service.title }}
              </h3>

              <p class="text-gray-300 leading-relaxed">
                {{ service.description }}
              </p>
            </div>
          </UCard>
        </div>
      </UContainer>
    </section>

    <!-- Features Section -->
    <section id="about" class="relative z-40 py-20 text-white">
      <UContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
            {{ t('features.title') }}
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            {{ t('features.subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(feature, index) in features" :key="index"
            class="group p-8 rounded-2xl backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-1">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center text-white transform group-hover:scale-110 transition-all duration-300">
                  <UIcon :name="feature.icon" class="text-xl" />
                </div>
              </div>

              <div class="flex-1">
                <h3 class="text-lg font-bold text-white mb-3">
                  {{ feature.title }}
                </h3>

                <p class="text-gray-300 leading-relaxed">
                  {{ feature.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </UContainer>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="relative z-40 py-20 text-white">
      <UContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
            {{ t('portfolio_section.title') }}
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            {{ t('portfolio_section.subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(project, index) in portfolioProjects" :key="index"
            class="group relative overflow-hidden rounded-2xl backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/15 transition-colors duration-300">

            <!-- 项目图片 -->
            <div class="relative h-48 overflow-hidden">
              <img :src="project.image" :alt="project.title" loading="lazy" class="w-full h-full object-cover" />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              </div>

              <!-- 项目类型标签 -->
              <div class="absolute top-4 left-4">
                <span class="px-3 py-1 text-xs font-semibold rounded-full" :class="getProjectTypeColor(project.type)">
                  {{ project.type }}
                </span>
              </div>
            </div>

            <!-- 项目信息 -->
            <div class="p-6">
              <h3 class="text-xl font-bold text-white mb-3">
                {{ project.title }}
              </h3>

              <p class="text-gray-300 text-sm mb-4 leading-relaxed">
                {{ project.description }}
              </p>

              <!-- 技术栈标签 -->
              <div class="flex flex-wrap gap-2">
                <span v-for="tech in project.technologies" :key="tech"
                  class="px-2 py-1 text-xs bg-white/10 text-white/80 rounded-md border border-white/20">
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </UContainer>
    </section>
  </div>
</template>

<style scoped>
/* 网格背景 */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(50px, 50px);
  }
}

/* 浮动动画 */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-20px) rotate(1deg);
  }

  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes float-delayed {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-15px) rotate(-1deg);
  }

  66% {
    transform: translateY(-25px) rotate(1deg);
  }
}

@keyframes float-slow {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-30px) rotate(2deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
  animation-delay: 4s;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* 鼠标悬浮效果 */
.hover-glow:hover {
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.5),
    0 0 40px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px) scale(1.05);
}

/* 按钮悬浮效果 */
.btn-hover:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.2),
    0 0 30px rgba(59, 130, 246, 0.3);
}

/* 卡片悬浮效果 */
.card-hover:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 50px rgba(139, 92, 246, 0.2);
}

/* 脉冲效果 */
@keyframes pulse-glow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    transform: scale(1);
  }

  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    transform: scale(1.02);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 文字打字机效果 */
@keyframes typewriter {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.75);
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {

  from,
  to {
    border-color: transparent;
  }

  50% {
    border-color: rgba(255, 255, 255, 0.75);
  }
}

/* 3D 变换效果 */
.transform-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.rotate-y:hover {
  transform: rotateY(10deg) rotateX(5deg);
}

/* 光线扫描效果 */
@keyframes scan {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100vw);
  }
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #00ffff, transparent);
  animation: scan 3s linear infinite;
}

/* 粒子连线效果 */
.particle-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  height: 1px;
  animation: particle-move 4s linear infinite;
}

@keyframes particle-move {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: translateX(100px);
    opacity: 0;
  }
}
</style>
