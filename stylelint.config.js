module.exports = {
  extends: [
    '@stylistic/stylelint-config',
    'stylelint-config-standard-scss',
    'stylelint-config-standard-vue',
    'stylelint-config-recess-order',
  ],
  plugins: [
    'stylelint-scss',
  ],
  rules: {
    // 自定义规则
    'selector-class-pattern': null,
    'keyframes-name-pattern': null,
    'stylistic/indentation': 2,
    'stylistic/semi': 'always',

    // SCSS 规则
    'scss/at-rule-no-unknown': true,
    'scss/dollar-variable-pattern': /^[a-z][a-z0-9-]*$/,

    // Vue 特定规则
    'selector-pseudo-class-no-unknown': [
      true,
      { ignorePseudoClasses: ['deep', 'global'] },
    ],
  },
  overrides: [
    {
      files: ['**/*.vue'],
      customSyntax: 'postcss-html',
    },
    {
      files: ['**/*.scss'],
      customSyntax: 'postcss-scss',
    },
  ],
  ignoreFiles: [
    '**/node_modules/**',
    '**/dist/**',
  ],
}
