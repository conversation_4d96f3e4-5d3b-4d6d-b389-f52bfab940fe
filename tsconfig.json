{
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "~~/*": ["./*"],
      "@@/*": ["./*"],
      "~/*": ["./*"],
    },
    "strict": true,
    "types": ["nuxt", "@nuxt/ui", "@nuxtjs/i18n", "vue-tsc"],
    "noUnusedLocals": true,
    "noUnusedParameters": true
  },
  "include": [
    "**/*.ts",
    "**/*.vue",
    ".nuxt/**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".output"
  ]
}